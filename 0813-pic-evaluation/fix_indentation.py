#!/usr/bin/env python3
"""
修复test_oss.py文件的缩进问题
"""

def fix_indentation():
    # 读取原文件
    with open('test_oss_backup.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 修复后的内容
    fixed_lines = []
    in_for_loop = False
    
    for i, line in enumerate(lines):
        line_num = i + 1
        
        # 检测for循环开始
        if line.strip().startswith('for local_file in files:'):
            in_for_loop = True
            fixed_lines.append(line)
            continue
        
        # 如果在for循环内，修复缩进
        if in_for_loop:
            # 如果是空行，保持原样
            if line.strip() == '':
                fixed_lines.append(line)
                continue
            
            # 如果是下一个顶级语句，结束for循环
            if line.startswith('print(f"\\n🎉') or (line.strip() and not line.startswith(' ') and not line.startswith('\t')):
                in_for_loop = False
                fixed_lines.append(line)
                continue
            
            # 修复for循环内的缩进
            stripped = line.lstrip()
            if stripped:
                # 统一使用4个空格缩进
                fixed_lines.append('    ' + stripped)
            else:
                fixed_lines.append(line)
        else:
            fixed_lines.append(line)
    
    # 写入修复后的文件
    with open('test_oss.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print("✅ 缩进修复完成！")

if __name__ == "__main__":
    fix_indentation()
