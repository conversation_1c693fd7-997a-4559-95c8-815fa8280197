import oss2
import os
import pandas as pd
from datetime import datetime
# OSS配置信息

# 启动 .env 环境 
from dotenv import load_dotenv
load_dotenv()

endpoint = 'oss-cn-beijing.aliyuncs.com'  # 如 'oss-cn-hangzhou.aliyuncs.com'
access_key_id = os.getenv("access_key_id")
access_key_secret = os.getenv("access_key_secret")
bucket_name = 'test-sl-bj-oss-multi-modal'


import os

# 获取当前目录所有文件（递归）
files = []
for root, dirs, filenames in os.walk("/Users/<USER>/Downloads/测试照片/"):
    for filename in filenames:
        file_path = os.path.join(root, filename)
        files.append(file_path)
print("files:",files)
bucket = oss2.Bucket(oss2.Auth(access_key_id, access_key_secret), endpoint, bucket_name)

local_file = '/Users/<USER>/Downloads/测试照片/7_酒/camera_20250803_152455_0_left.jpg'

for local_file in files:
   object_name = "script/" + local_file.removeprefix("/Users/<USER>/Downloads/测试照片/")
   print("object_name:",object_name)
# object_name = 'script/7_酒/camera_20250803_152455_0_left.jpg'


    bucket.put_object_from_file(object_name, local_file)

    # 生成签名URL，有效期3600秒（1小时）
    url_graph = bucket.sign_url('GET', object_name, 3600*2)
    print("短期图片链接:", url_graph)



    import requests
    import json

    ### 图片检索
    X_Api_Key = os.getenv("X_Api_Key")
    # API 地址
    url = "https://gw-openapi.zhidemai.com/img-search-service/v1/img_search_by_pic"

    # 请求头
    headers = {
        "Content-Type": "application/json",
        "X-Api-Key": X_Api_Key
    }

    # 请求数据
    payload = {
        "img_url": url_graph,
        "img_num": 10,
        "is_auto_first_cate_name": 1
    }

    # 发送 POST 请求
    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload))

        # 检查响应状态
        if response.status_code == 200:
            result = response.json()
            print("✅ 请求成功，返回结果如下：")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 请求失败，状态码：{response.status_code}")
            print("返回内容：", response.text)

    except requests.exceptions.RequestException as e:
        print("🚨 请求异常：", e)

    # 帮我生成吧 文件路径和获取的结果都写到Excel表里面

    # 将结果写入Excel

    # 准备Excel数据
    excel_data = []

    if response.status_code == 200 and 'result' in locals():
        # 解析API返回的结果
        if 'data' in result and 'items' in result['data']:
            items = result['data']['items']
            for item in items:
                excel_row = {
                    '文件路径': local_file,
                    '对象名称': object_name,
                    '图片URL': url_graph,
                    '商品标题': item.get('title', ''),
                    '商品价格': item.get('price', ''),
                    '商品链接': item.get('url', ''),
                    '相似度': item.get('similarity', ''),
                    '分类': item.get('category', ''),
                    '品牌': item.get('brand', ''),
                    '请求时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    '响应状态': 'SUCCESS'
                }
                excel_data.append(excel_row)
        else:
            # 如果没有找到商品数据，也记录基本信息
            excel_row = {
                '文件路径': local_file,
                '对象名称': object_name,
                '图片URL': url_graph,
                '商品标题': '未找到匹配商品',
                '商品价格': '',
                '商品链接': '',
                '相似度': '',
                '分类': '',
                '品牌': '',
                '请求时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                '响应状态': 'NO_RESULTS'
            }
            excel_data.append(excel_row)
    else:
        # 请求失败的情况
        excel_row = {
            '文件路径': local_file,
            '对象名称': object_name,
            '图片URL': url_graph,
            '商品标题': '',
            '商品价格': '',
            '商品链接': '',
            '相似度': '',
            '分类': '',
            '品牌': '',
            '请求时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '响应状态': f'FAILED_{response.status_code}' if 'response' in locals() else 'ERROR'
        }
        excel_data.append(excel_row)

    # 将数据追加到Excel文件
    excel_file = '图片检索结果.xlsx'

    try:
        # 尝试读取现有的Excel文件
        existing_df = pd.read_excel(excel_file)
        # 将新数据追加到现有数据
        new_df = pd.DataFrame(excel_data)
        combined_df = pd.concat([existing_df, new_df], ignore_index=True)
    except FileNotFoundError:
        # 如果文件不存在，创建新的DataFrame
        combined_df = pd.DataFrame(excel_data)

    # 保存到Excel文件
    combined_df.to_excel(excel_file, index=False)
    print(f"✅ 结果已保存到 {excel_file}")
    print(f"📊 当前文件共有 {len(combined_df)} 条记录")


