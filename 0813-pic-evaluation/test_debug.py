import os
import pandas as pd
from datetime import datetime

# 测试Excel写入功能
def test_excel_functionality():
    print("🧪 测试Excel写入功能...")
    
    # 模拟数据
    test_data = [
        {
            '文件路径': '/test/path/image1.jpg',
            '对象名称': 'script/image1.jpg',
            '图片URL': 'https://example.com/image1.jpg',
            '商品标题': '测试商品1',
            '商品价格': '99.99',
            '商品链接': 'https://example.com/product1',
            '相似度': '0.95',
            '分类': '电子产品',
            '品牌': '测试品牌',
            '请求时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '响应状态': 'SUCCESS'
        },
        {
            '文件路径': '/test/path/image2.jpg',
            '对象名称': 'script/image2.jpg',
            '图片URL': 'https://example.com/image2.jpg',
            '商品标题': '测试商品2',
            '商品价格': '199.99',
            '商品链接': 'https://example.com/product2',
            '相似度': '0.88',
            '分类': '服装',
            '品牌': '测试品牌2',
            '请求时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '响应状态': 'SUCCESS'
        }
    ]
    
    excel_file = '测试图片检索结果.xlsx'
    
    try:
        # 尝试读取现有的Excel文件
        existing_df = pd.read_excel(excel_file)
        print(f"📖 读取到现有数据 {len(existing_df)} 条")
        # 将新数据追加到现有数据
        new_df = pd.DataFrame(test_data)
        combined_df = pd.concat([existing_df, new_df], ignore_index=True)
    except FileNotFoundError:
        print("📝 创建新的Excel文件")
        # 如果文件不存在，创建新的DataFrame
        combined_df = pd.DataFrame(test_data)
    
    # 保存到Excel文件
    combined_df.to_excel(excel_file, index=False)
    print(f"✅ 测试结果已保存到 {excel_file}")
    print(f"📊 当前文件共有 {len(combined_df)} 条记录")
    
    # 验证文件内容
    verify_df = pd.read_excel(excel_file)
    print(f"🔍 验证：文件中实际有 {len(verify_df)} 条记录")
    print("前3行数据预览：")
    print(verify_df.head(3).to_string())
    
    return True

def test_api_response_parsing():
    print("\n🧪 测试API响应解析...")
    
    # 模拟API响应
    mock_response = {
        "code": 200,
        "message": "success",
        "data": {
            "items": [
                {
                    "title": "iPhone 15 Pro",
                    "price": "7999",
                    "url": "https://example.com/iphone15",
                    "similarity": "0.95",
                    "category": "手机",
                    "brand": "Apple"
                },
                {
                    "title": "Samsung Galaxy S24",
                    "price": "6999",
                    "url": "https://example.com/galaxy",
                    "similarity": "0.88",
                    "category": "手机",
                    "brand": "Samsung"
                }
            ]
        }
    }
    
    excel_data = []
    local_file = "/test/phone.jpg"
    object_name = "script/phone.jpg"
    url_graph = "https://oss.example.com/phone.jpg"
    
    # 解析响应数据
    if 'data' in mock_response and 'items' in mock_response['data']:
        items = mock_response['data']['items']
        for item in items:
            excel_row = {
                '文件路径': local_file,
                '对象名称': object_name,
                '图片URL': url_graph,
                '商品标题': item.get('title', ''),
                '商品价格': item.get('price', ''),
                '商品链接': item.get('url', ''),
                '相似度': item.get('similarity', ''),
                '分类': item.get('category', ''),
                '品牌': item.get('brand', ''),
                '请求时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                '响应状态': 'SUCCESS'
            }
            excel_data.append(excel_row)
    
    print(f"✅ 成功解析 {len(excel_data)} 条商品数据")
    for i, row in enumerate(excel_data):
        print(f"商品 {i+1}: {row['商品标题']} - {row['商品价格']}元")
    
    return excel_data

if __name__ == "__main__":
    print("🚀 开始调试测试...")
    
    # 测试Excel功能
    test_excel_functionality()
    
    # 测试API解析
    test_api_response_parsing()
    
    print("\n✅ 所有测试完成！")
