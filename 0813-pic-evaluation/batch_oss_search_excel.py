# -*- coding: utf-8 -*-
"""
批量遍历 /Users/<USER>/Downloads/测试照片 下的图片：
1) 上传到阿里云 OSS，生成可访问链接
2) 调用图片检索接口（is_auto_first_cate_name=0），拿到结果
3) 将每张图的“真实标签(测试照片下一级目录名) + 上传链接 + 检索结果”等写入 Excel

注意：
- 不打开其他文件；脚本仅访问图片文件、并通过参数/环境变量读取必要配置。
- 图片检索接口通过 --api-url 传入，鉴权可用 --api-key 或环境变量 X-Api-Key。

运行示例：
  python3 batch_oss_search_excel.py \
    --api-url "https://your-search.example.com/api/search" \
    --api-key "$X_Api_Key" \
    --base-dir "/Users/<USER>/Downloads/测试照片" \
    --output "results.xlsx"

必要依赖：oss2, requests, openpyxl
  pip install oss2 requests openpyxl
"""
from __future__ import annotations
import os
import sys
import json
import time
import argparse
import traceback
from typing import Dict, Any, List, Tuple

import requests
import oss2

try:
    # openpyxl 用来写 xlsx
    from openpyxl import Workbook
    from openpyxl.utils import get_column_letter
except Exception as e:
    Workbook = None

IMAGE_EXTS = {".jpg", ".jpeg", ".png", ".bmp", ".webp", ".gif"}


def is_image_file(path: str) -> bool:
    ext = os.path.splitext(path)[1].lower()
    return ext in IMAGE_EXTS


def iter_images_with_label(base_dir: str) -> List[Tuple[str, str, str]]:
    """
    遍历 base_dir 下所有图片。真实标签为相对 base_dir 的第一层目录名。
    返回 (abs_path, rel_path, label) 列表
    其中：
      - abs_path: 图片绝对路径
      - rel_path: 图片相对 base_dir 的路径（用于在 OSS 上保持层级）
      - label: 第一层目录名
    """
    results: List[Tuple[str, str, str]] = []
    base_dir = os.path.abspath(base_dir)
    for root, _, files in os.walk(base_dir):
        for fname in files:
            abs_path = os.path.join(root, fname)
            if not is_image_file(abs_path):
                continue
            rel_path = os.path.relpath(abs_path, base_dir)
            parts = rel_path.split(os.sep)
            if len(parts) < 2:
                # 直接放在 base_dir 根下的文件，无法确定“下一级目录=真实标签”，跳过
                continue
            label = parts[0]
            results.append((abs_path, rel_path, label))
    return results


def build_bucket(endpoint: str, access_key_id: str, access_key_secret: str, bucket_name: str) -> oss2.Bucket:
    auth = oss2.Auth(access_key_id, access_key_secret)
    bucket = oss2.Bucket(auth, endpoint, bucket_name)
    return bucket


def upload_and_sign(bucket: oss2.Bucket, object_name: str, local_file: str, sign_expires: int = 7200, retry: int = 3, backoff: float = 1.5) -> Tuple[bool, str, str]:
    """
    上传文件并生成签名 URL。
    返回 (ok, url, err)
    """
    last_err = ""
    for i in range(retry):
        try:
            bucket.put_object_from_file(object_name, local_file)
            url = bucket.sign_url('GET', object_name, sign_expires)
            return True, url, ""
        except Exception as e:
            last_err = f"upload_sign_error[{i+1}/{retry}]: {e}"
            time.sleep(backoff ** i)
    return False, "", last_err


def call_search_api(api_url: str, api_key: str | None, img_url: str, is_auto_first_cate_name: int = 0, timeout: float = 15.0) -> Tuple[bool, Dict[str, Any] | None, str, str]:
    """
    调用图片检索接口，返回 (ok, json_obj, raw_text, err)
    请求体包含：link/url 与 is_auto_first_cate_name=0
    """
    headers = {"Content-Type": "application/json"}
    if api_key:
        headers["X-Api-Key"] = api_key

    payload = {
        # 同时提供常见字段名，服务端取其一即可
        "link": img_url,
        "url": img_url,
        "image_url": img_url,
        "is_auto_first_cate_name": int(is_auto_first_cate_name),
    }

    try:
        resp = requests.post(api_url, headers=headers, json=payload, timeout=timeout)
        raw = resp.text
        if resp.status_code != 200:
            return False, None, raw, f"HTTP {resp.status_code}"
        try:
            data = resp.json()
            return True, data, raw, ""
        except Exception as je:
            return False, None, raw, f"JSON parse error: {je}"
    except Exception as e:
        return False, None, "", f"request error: {e}"


def ensure_xlsx_writer(output_path: str):
    if Workbook is None:
        raise RuntimeError("openpyxl 未安装，请执行: pip install openpyxl")


def write_results_xlsx(output_path: str, rows: List[Dict[str, Any]]):
    ensure_xlsx_writer(output_path)
    wb = Workbook()
    ws = wb.active
    ws.title = "results"

    # 标题行
    headers = [
        "index",
        "local_path",
        "label",
        "oss_object",
        "signed_url",
        "is_auto_first_cate_name",
        "api_error_code",
        "api_error_msg",
        "item_name",
        "first_cate_name",
        "total_seconds",
        "nickname",
        "ip",
        "data_json",
        "response_raw",
    ]
    ws.append(headers)

    for idx, row in enumerate(rows, 1):
        ws.append([
            idx,
            row.get("local_path", ""),
            row.get("label", ""),
            row.get("oss_object", ""),
            row.get("signed_url", ""),
            row.get("is_auto_first_cate_name", 0),
            row.get("api_error_code", ""),
            row.get("api_error_msg", ""),
            row.get("item_name", ""),
            row.get("first_cate_name", ""),
            row.get("total_seconds", ""),
            row.get("nickname", ""),
            row.get("ip", ""),
            row.get("data_json", ""),
            row.get("response_raw", ""),
        ])

    # 调整列宽（某些最小实现/替代 Worksheet 可能不支持列宽设置，故容错）
    for col_idx in range(1, len(headers) + 1):
        try:
            ws.column_dimensions[get_column_letter(col_idx)].width = 26
        except Exception:
            # 忽略列宽设置失败
            pass

    wb.save(output_path)


def main():
    parser = argparse.ArgumentParser(description="批量上传 OSS + 图片检索 + 写 Excel")
    parser.add_argument("--base-dir", default="/Users/<USER>/Downloads/测试照片", help="图片根目录，一级子目录名作为真实标��")
    parser.add_argument("--endpoint", default="oss-cn-beijing.aliyuncs.com", help="OSS endpoint")
    parser.add_argument("--access-key-id", default=os.environ.get("OSS_ACCESS_KEY_ID", "LTAI5tMt4VYdyQB2ES3BgLdn"), help="OSS access key id")
    parser.add_argument("--access-key-secret", default=os.environ.get("OSS_ACCESS_KEY_SECRET", "******************************"), help="OSS access key secret")
    parser.add_argument("--bucket-name", default="test-sl-bj-oss-multi-modal", help="OSS bucket 名称")
    parser.add_argument("--object-prefix", default="script", help="OSS 对象前缀(如 script)")
    parser.add_argument("--sign-expires", type=int, default=7200, help="签名 URL 有效期(秒)")

    parser.add_argument("--api-url", required=True, help="图片检索接口 URL (POST JSON)")
    parser.add_argument("--api-key", default=os.environ.get("X-Api-Key") or os.environ.get("X_API_KEY"), help="图片检索接口鉴权键，写到请求头 X-Api-Key")
    parser.add_argument("--is-auto-first-cate-name", type=int, default=0, help="是否自动一级分类，0 代表图片检索排序")

    parser.add_argument("--output", default="results.xlsx", help="输出 Excel 文件路径(.xlsx)")
    parser.add_argument("--retry", type=int, default=3, help="上传/请求重试次数")
    parser.add_argument("--timeout", type=float, default=15.0, help="图片检索接口超时(秒)")

    args = parser.parse_args()

    base_dir = args.base_dir
    if not os.path.isdir(base_dir):
        print(f"[FATAL] base-dir 不存在: {base_dir}")
        sys.exit(2)

    images = iter_images_with_label(base_dir)
    if not images:
        print(f"[WARN] 未找到图片: {base_dir}")

    bucket = build_bucket(args.endpoint, args.access_key_id, args.access_key_secret, args.bucket_name)

    all_rows: List[Dict[str, Any]] = []
    t0 = time.time()

    for abs_path, rel_path, label in images:
        try:
            # OSS object 名保持相对路径层级
            oss_object = "/".join([args.object_prefix.strip("/"), rel_path.replace(os.sep, "/")])

            ok, url, err = upload_and_sign(bucket, oss_object, abs_path, sign_expires=args.sign_expires, retry=args.retry)
            if not ok:
                row = {
                    "local_path": abs_path,
                    "label": label,
                    "oss_object": oss_object,
                    "signed_url": "",
                    "is_auto_first_cate_name": int(args.is_auto_first_cate_name),
                    "api_error_code": -1,
                    "api_error_msg": f"OSS上传失败: {err}",
                    "response_raw": "",
                }
                all_rows.append(row)
                print(f"[ERR] OSS 上传失败 label={label} file={abs_path} err={err}")
                continue

            # 调用图片检索接口
            ok2, data_obj, raw_text, req_err = call_search_api(
                args.api_url,
                args.api_key,
                url,
                is_auto_first_cate_name=args.is_auto_first_cate_name,
                timeout=args.timeout,
            )

            # 预设字段
            item_name = ""
            first_cate_name = ""
            nickname = ""
            ip = ""
            total_seconds = ""
            api_error_code = ""
            api_error_msg = ""
            data_json = ""

            if ok2 and isinstance(data_obj, dict):
                # 解析标准返回
                api_error_code = data_obj.get("error_code", "")
                api_error_msg = data_obj.get("error_msg", "")
                item_name = data_obj.get("item_name", "")
                first_cate_name = data_obj.get("first_cate_name", "")
                nickname = data_obj.get("nickname", "")
                ip = data_obj.get("ip", "")
                total_seconds = data_obj.get("total_seconds", "")

                try:
                    data_json = json.dumps(data_obj.get("data", []), ensure_ascii=False)
                except Exception:
                    data_json = ""
            else:
                api_error_code = -1
                api_error_msg = req_err or "未知错误"

            row = {
                "local_path": abs_path,
                "label": label,
                "oss_object": oss_object,
                "signed_url": url,
                "is_auto_first_cate_name": int(args.is_auto_first_cate_name),
                "api_error_code": api_error_code,
                "api_error_msg": api_error_msg,
                "item_name": item_name,
                "first_cate_name": first_cate_name,
                "total_seconds": total_seconds,
                "nickname": nickname,
                "ip": ip,
                "data_json": data_json,
                "response_raw": raw_text,
            }
            all_rows.append(row)

            print(f"[OK] label={label} file={abs_path} -> {url}")
        except Exception:
            tb = traceback.format_exc()
            row = {
                "local_path": abs_path,
                "label": label,
                "oss_object": oss_object if 'oss_object' in locals() else '',
                "signed_url": url if 'url' in locals() else '',
                "is_auto_first_cate_name": int(args.is_auto_first_cate_name),
                "api_error_code": -1,
                "api_error_msg": tb,
                "response_raw": "",
            }
            all_rows.append(row)
            print(f"[ERR] 处理失败 label={label} file={abs_path}\n{tb}")

    # 写 Excel
    write_results_xlsx(args.output, all_rows)

    cost = time.time() - t0
    print(f"完成，共处理 {len(all_rows)} 张图，耗时 {cost:.2f}s，已写 {args.output}")


if __name__ == "__main__":
    main()
