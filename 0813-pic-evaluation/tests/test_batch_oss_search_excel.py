import os
import tempfile
import shutil
import types
import unittest
from unittest import mock

import batch_oss_search_excel as batch


class TestBatchOssSearchExcel(unittest.TestCase):
    def test_is_image_file_behavior(self):
        self.assertTrue(batch.is_image_file('a.jpg'))
        self.assertTrue(batch.is_image_file('a.JPEG'))
        self.assertTrue(batch.is_image_file('a.PNG'))
        self.assertFalse(batch.is_image_file('a.txt'))
        self.assertFalse(batch.is_image_file('a'))

    def test_iter_images_with_label_returns_label(self):
        base = tempfile.mkdtemp()
        try:
            # Files at root should be skipped (no first-level directory/label)
            with open(os.path.join(base, 'root.jpg'), 'wb') as f:
                f.write(b'1')

            # First label dir
            label_a = os.path.join(base, 'LabelA')
            os.makedirs(label_a, exist_ok=True)
            with open(os.path.join(label_a, 'img1.jpg'), 'wb') as f:
                f.write(b'1')
            with open(os.path.join(label_a, 'readme.txt'), 'w', encoding='utf-8') as f:
                f.write('not image')

            # Nested under another label dir
            label_b = os.path.join(base, 'LabelB')
            nested = os.path.join(label_b, 'sub')
            os.makedirs(nested, exist_ok=True)
            with open(os.path.join(nested, 'img2.png'), 'wb') as f:
                f.write(b'1')

            results = batch.iter_images_with_label(base)
            # Expect two images discovered
            rels = sorted([r[1] for r in results])
            labels = sorted([r[2] for r in results])
            self.assertEqual(len(results), 2)
            self.assertIn('LabelA' + os.sep + 'img1.jpg', rels)
            self.assertIn('LabelB' + os.sep + 'sub' + os.sep + 'img2.png', rels)
            self.assertEqual(labels, ['LabelA', 'LabelB'])
        finally:
            shutil.rmtree(base)

    def test_call_search_api_success_and_headers(self):
        fake_response_json = {
            "error_code": 0,
            "error_msg": "",
            "item_name": "庐山香烟",
            "first_cate_name": "日用百货",
            "data": [],
            "total_seconds": 2.47,
            "nickname": "",
            "ip": "*.*.*.99",
        }

        captured = {}

        class FakeResp:
            status_code = 200
            text = '{"ok":true}'

            def json(self):
                return fake_response_json

        def fake_post(url, headers=None, json=None, timeout=None):
            captured['url'] = url
            captured['headers'] = headers
            captured['json'] = json
            captured['timeout'] = timeout
            return FakeResp()

        with mock.patch('requests.post', side_effect=fake_post) as _:
            ok, data, raw_text, err = batch.call_search_api(
                api_url='https://api.example.com/search',
                api_key='25767',
                img_url='http://signed.url/img.jpg',
                is_auto_first_cate_name=0,
                timeout=12.0,
            )
        self.assertTrue(ok)
        self.assertIsInstance(data, dict)
        self.assertEqual(data['error_code'], 0)
        self.assertEqual(captured['headers'].get('X-Api-Key'), '25767')
        self.assertEqual(captured['json']['is_auto_first_cate_name'], 0)
        # Accept any of these link fields
        self.assertEqual(captured['json']['url'], 'http://signed.url/img.jpg')

    def test_call_search_api_http_error(self):
        class FakeResp:
            status_code = 500
            text = 'server error'

            def json(self):
                return {"not": "json"}

        with mock.patch('requests.post', return_value=FakeResp()):
            ok, data, raw_text, err = batch.call_search_api(
                api_url='https://api.example.com/search',
                api_key=None,
                img_url='http://x/y.jpg',
                is_auto_first_cate_name=0,
                timeout=1.0,
            )
        self.assertFalse(ok)
        self.assertIsNone(data)
        self.assertEqual(raw_text, 'server error')
        self.assertIn('HTTP 500', err)

    def test_upload_and_sign_success(self):
        calls = {'put': 0, 'sign': 0}

        class FakeBucket:
            def put_object_from_file(self, object_name, local_file):
                calls['put'] += 1

            def sign_url(self, method, object_name, expires):
                calls['sign'] += 1
                return 'http://signed/link.jpg'

        ok, url, err = batch.upload_and_sign(
            bucket=FakeBucket(),
            object_name='script/a/b.jpg',
            local_file=__file__,
            sign_expires=10,
            retry=2,
        )
        self.assertTrue(ok)
        self.assertEqual(url, 'http://signed/link.jpg')
        self.assertEqual(calls['put'], 1)
        self.assertEqual(calls['sign'], 1)
        self.assertEqual(err, '')

    def test_upload_and_sign_retry_then_success(self):
        calls = {'put': 0}

        class FakeBucket:
            def put_object_from_file(self, object_name, local_file):
                calls['put'] += 1
                if calls['put'] == 1:
                    raise RuntimeError('temp error')

            def sign_url(self, method, object_name, expires):
                return 'http://signed/ok.jpg'

        ok, url, err = batch.upload_and_sign(
            bucket=FakeBucket(),
            object_name='script/x.jpg',
            local_file=__file__,
            sign_expires=10,
            retry=3,
            backoff=0.01,
        )
        self.assertTrue(ok)
        self.assertEqual(url, 'http://signed/ok.jpg')
        self.assertEqual(calls['put'], 2)
        self.assertEqual(err, '')

    def test_ensure_xlsx_writer_raises_when_no_openpyxl(self):
        with mock.patch.object(batch, 'Workbook', None):
            with self.assertRaises(RuntimeError):
                batch.ensure_xlsx_writer('out.xlsx')

    def test_write_results_xlsx_with_fake_workbook(self):
        # Fake minimal Workbook/Worksheet to capture writes
        class CD:
            def __init__(self):
                self.width = None

        class FakeWorksheet:
            def __init__(self):
                self.title = ''
                self.rows = []
                self.column_dimensions = {}

            def append(self, row):
                self.rows.append(row)

            def __getitem__(self, k):
                return self.column_dimensions.setdefault(k, CD())

        class FakeWorkbook:
            def __init__(self):
                self.active = FakeWorksheet()
                self.saved_path = None

            def save(self, path):
                self.saved_path = path

        def fake_get_column_letter(idx):
            # Return simple letters A, B, C, ... up to Z, then AA...
            import string
            letters = string.ascii_uppercase
            s = ''
            while idx:
                idx, rem = divmod(idx - 1, 26)
                s = letters[rem] + s
            return s

        with mock.patch.object(batch, 'Workbook', FakeWorkbook), \
             mock.patch.object(batch, 'get_column_letter', side_effect=fake_get_column_letter):
            out = os.path.join(tempfile.gettempdir(), 'ut_results.xlsx')
            rows = [{
                'local_path': '/a/b.jpg',
                'label': '标签',
                'oss_object': 'script/a/b.jpg',
                'signed_url': 'http://s',
                'is_auto_first_cate_name': 0,
                'api_error_code': 0,
                'api_error_msg': '',
                'item_name': '庐山香烟',
                'first_cate_name': '日用百货',
                'total_seconds': 1.23,
                'nickname': '',
                'ip': '*.*.*.99',
                'data_json': '[]',
                'response_raw': '{"ok":true}',
            }]
            batch.write_results_xlsx(out, rows)

            # Validate minimal expectations
            wb = batch.Workbook()
            ws = wb.active
            # headers length
            self.assertGreaterEqual(len(ws.rows[0]), 10)


if __name__ == '__main__':
    unittest.main(verbosity=2)
