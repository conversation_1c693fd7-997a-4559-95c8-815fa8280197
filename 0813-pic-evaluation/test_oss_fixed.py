import oss2
import os
import pandas as pd
from datetime import datetime
import requests
import json
# OSS配置信息

# 启动 .env 环境 
from dotenv import load_dotenv
load_dotenv()

endpoint = 'oss-cn-beijing.aliyuncs.com'  # 如 'oss-cn-hangzhou.aliyuncs.com'
access_key_id = os.getenv("access_key_id")
access_key_secret = os.getenv("access_key_secret")
bucket_name = 'test-sl-bj-oss-multi-modal'
# import requests, json

# # url = 'https://gw-openapi.zhidemai.com/img-search-service/v1/img_search_by_pic'
# headers = {
#     'Content-Type': 'application/json',
#     'X-Api-Key': '25767dd78548ff5e590f4f02fa62ca00'
# }
# payload = {
#     "img_url": "http://test-sl-bj-oss-multi-modal.oss-cn-beijing.aliyuncs.com/script%2F7_%E9%85%92%2Fcamera_20250803_152455_1_right.jpg?OSSAccessKeyId=LTAI5tMt4VYdyQB2ES3BgLdn&Expires=1755088439&Signature=QlFBBB3DivJReP5PEYjVcvQ2WuE%3D",
#     "img_num": 10,
#     "is_auto_first_cate_name": 1
# }

# resp = requests.post(url, headers=headers, data=json.dumps(payload), timeout=15)
# print('返回码:', resp.status_code)
# print('返回体:', json.dumps(resp.json(), ensure_ascii=False, indent=2))
# 获取当前目录所有文件（递归）
files = []
for root, dirs, filenames in os.walk("/Users/<USER>/Downloads/测试照片/"):
    for filename in filenames:
        file_path = os.path.join(root, filename)
        files.append(file_path)
print("files:", files)

bucket = oss2.Bucket(oss2.Auth(access_key_id, access_key_secret), endpoint, bucket_name)

### 图片检索API配置
X_Api_Key = "25767dd78548ff5e590f4f02fa62ca00"
# os.getenv("X_Api_Key")
print("")
# API 地址
api_url = "https://gw-openapi.zhidemai.com/img-search-service/v1/img_search_by_pic"

# 请求头
headers = {
    "Content-Type": "application/json",
    "X-Api-Key": X_Api_Key
}

# 处理每个文件
for local_file in files:
    print(f"\n🔄 正在处理文件: {local_file}")
    
    # 生成OSS对象名称
    object_name = "script/" + local_file.removeprefix("/Users/<USER>/Downloads/测试照片/")
    print("object_name:", object_name)
    
    try:
        # 上传文件到OSS
        bucket.put_object_from_file(object_name, local_file)
        
        # 生成签名URL，有效期2小时
        url_graph = bucket.sign_url('GET', object_name, 3600*2)
        print("短期图片链接:", url_graph)

        # 请求数据
        payload = {
            "img_url": url_graph,
            "img_num": 10,
            "is_auto_first_cate_name": 1
        }

        # 初始化Excel数据列表
        excel_data = []

        # 发送 POST 请求
        try:
            response = requests.post(api_url, headers=headers, data=json.dumps(payload), timeout=15)

            # 检查响应状态
            if response.status_code == 200:
                result = response.json()
                print("✅ 请求成功，返回结果如下：")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                # 解析API返回的结果
                if 'data' in result and 'items' in result['data']:
                    items = result['data']['items']
                    for item in items:
                        excel_row = {
                        '文件路径': local_file,
                        '对象名称': object_name,
                        '图片URL': url_graph,
                        '商品名称': item.get('pro_name', ''),
                        '商品链接': item.get('pro_url', ''),
                        '商品图片': item.get('img_url', ''),
                        '品牌': item.get('brand_name', ''),
                        '相似度分数': item.get('score', ''),
                        '一级分类': item.get('cate_name_1', ''),
                        '二级分类': item.get('cate_name_2', ''),
                        '三级分类': item.get('cate_name_3', ''),
                        '四级分类': item.get('cate_name_4', ''),
                        '商品ID': item.get('pro_id', ''),
                        '品牌ID': item.get('brand_id', ''),
                        '商家ID': item.get('business_id', ''),
                        '产品ID': item.get('product_id', ''),
                        '识别物品': result.get('item_name', ''),
                        '主分类': result.get('first_cate_name', ''),
                        '处理时间': f"{result.get('total_seconds', '')}秒",
                        '请求时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        '响应状态': 'SUCCESS'
                    }
                        excel_data.append(excel_row)
                # else:
                #     # 如果没有找到商品数据，也记录基本信息
                #     excel_row = {
                #         '文件路径': local_file,
                #         '对象名称': object_name,
                #         '图片URL': url_graph,
                #         '商品标题': '未找到匹配商品',
                #         '商品价格': '',
                #         '商品链接': '',
                #         '相似度': '',
                #         '分类': '',
                #         '品牌': '',
                #         '请求时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                #         '响应状态': 'NO_RESULTS'
                #     }
                #     excel_data.append(excel_row)
            else:
                print(f"❌ 请求失败，状态码：{response.status_code}")
                print("返回内容：", response.text)
                
                # 请求失败的情况
                excel_row = {
                    '文件路径': local_file,
                    '对象名称': object_name,
                    '图片URL': url_graph,
                    '商品标题': '',
                    '商品价格': '',
                    '商品链接': '',
                    '相似度': '',
                    '分类': '',
                    '品牌': '',
                    '请求时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    '响应状态': f'FAILED_{response.status_code}'
                }
                excel_data.append(excel_row)

        except requests.exceptions.RequestException as e:
            print("🚨 请求异常：", e)
            
            # 请求异常的情况
            excel_row = {
                '文件路径': local_file,
                '对象名称': object_name,
                '图片URL': url_graph,
                '商品标题': '',
                '商品价格': '',
                '商品链接': '',
                '相似度': '',
                '分类': '',
                '品牌': '',
                '请求时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                '响应状态': f'ERROR: {str(e)}'
            }
            excel_data.append(excel_row)

        # 将数据追加到Excel文件
        excel_file = '图片检索结果.xlsx'

        try:
            # 尝试读取现有的Excel文件
            existing_df = pd.read_excel(excel_file)
            # 将新数据追加到现有数据
            new_df = pd.DataFrame(excel_data)
            combined_df = pd.concat([existing_df, new_df], ignore_index=True)
        except FileNotFoundError:
            # 如果文件不存在，创建新的DataFrame
            combined_df = pd.DataFrame(excel_data)

        # 保存到Excel文件
        combined_df.to_excel(excel_file, index=False)
        print(f"✅ 结果已保存到 {excel_file}")
        print(f"📊 当前处理文件: {os.path.basename(local_file)}")
        
    except Exception as e:
        print(f"❌ 处理文件 {local_file} 时出错: {str(e)}")
        continue

print(f"\n🎉 所有文件处理完成！最终Excel文件: 图片检索结果.xlsx")
